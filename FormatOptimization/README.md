# 依赖
本工具仅使用 Python 3.9 内置包，无需额外安装第三方依赖。

# 问题背景
在编写代码的过程中，开发者通常会添加大量的注释来记录调试信息、测试思路或临时修改的内容，同时可能会引入一些并未实际使用的包。这些多余的注释和导入的包在功能开发和测试阶段可能是必要的，但在最终实现功能后，它们可能会影响代码的整洁性和可读性。因此，开发者通常需要手动清理这些冗余内容，以保持代码的简洁性和可维护性。
本Prompt的目标是自动清理冗余的注释和未使用的导入包，同时，保留必要的注释，确保代码的可读性和可维护性。

# 用例名称
代码精简优化-移除冗余注释与未使用的导入

# 测试方法描述
## 总体目标
1、准确识别并删除冗余注释： 能区分有价值的注释（如关键业务逻辑、复杂算法的解释）与无意义的注释（如调试代码、已废弃代码的注释）。 避免误删影响代码理解的注释。
2、正确识别并移除未使用的导入： 检测所有 import 语句，判断是否在代码中实际使用。 确保移除未使用导入后，代码仍能正常运行。
3、保持代码结构与可读性： 在删除冗余内容的同时，确保代码风格不受影响。 代码逻辑清晰，不破坏原有缩进、换行等格式。
## 本代码库预期目标
1、删除do_sign方法内部的多余注释，删除78～92行的多余注释。
2、移除第3行冗余导入的json包。
3、保留原始的方法注释、关键代码注释（如“# 计算签名”）。


# 中文 Prompt
请删除当前项目中所有冗余的注释和未使用的导入包，同时，保留必要的注释，以确保代码可读性和可维护性。

# English Prompt
Please remove all redundant comments and unused imports from the current project while keeping essential comments to maintain code readability and maintainability.

# 评分准则
1、准确删除所有冗余注释和未使用导入，且保留关键注释，代码逻辑清晰、可读性强：1
2、大部分冗余内容被正确移除，少量有用注释被误删或少量冗余内容未被删除，但整体可读性较好：0.7
3、部分冗余注释或未使用导入未被删除，或删除了部分重要注释，影响了代码理解：0.4
4、误删大量有用的注释或导入，导致代码难以理解，甚至影响正常运行：0


