import hmac
import hashlib
import json
import time

def sha256(secret: str, message: str) -> str:
    """
    使用 HMAC-SHA256 计算签名
    """
    hmac_obj = hmac.new(secret.encode('utf-8'), message.encode('utf-8'), hashlib.sha256)
    return hmac_obj.hexdigest().lower()

def format_key_value(key: str, value: str) -> str:
    """
    格式化 Key=Value
    """
    # return f"{key}:{value}"
    return f"{key}={value}"

def request_param(params: dict) -> str:
    """
    处理请求参数
    """
    res = "requestParam:"
    if not params:
        return res
    formatted_params = "&".join(format_key_value(k, v) for k, v in sorted(params.items()))
    return res + formatted_params

def request_body(body: str) -> str:
    """
    处理请求 Body
    """
    return f"requestBody:{body}" if body else "requestBody:"

def canonical_request(method: str, uri: str, params: dict, body: str) -> str:
    """
    生成规范化请求
    """
    cm = f"requestMethod:{method}"
    cu = f"requestURI:{uri}"
    cp = request_param(params)
    cb = request_body(body)
    return f"{cm}\n{cu}\n{cp}\n{cb}"

def do_sign(ak: str, sk: str, text: str, timestamp: str) -> str:
    """
    计算最终的签名
    """
    sign_key_info = f"v1/{ak}/{timestamp}"
    #print("sign_key_info: " + sign_key_info)
    sign_key = sha256(sign_key_info, sk)
    #print("sign_key: "+sign_key)
    #sign_key = sha256(sk, sign_key_info)
    sign_result = sha256(text, sign_key)
    #print("sign_result: "+sign_result)
    return f"{sign_key_info}/{sign_result}"

def sign(ak: str, sk: str, method: str, uri: str, params: dict, body: str, timestamp: str) -> str:
    """
    计算签名
    """
    text = canonical_request(method, uri, params, body)
    print("text\n" + text)
    return do_sign(ak, sk, text, timestamp)


if __name__ == "__main__":


    ak = "1exxxx7e9d"
    sk = "5901axsxsx6a4fa4a5d34bdc555901ce"
    method = "GET"
    uri = "/api/rest/test/env-deploy/232323323/details"
    params = {}
    body = ""

    # ak = "33af2bd376"
    # sk = "949f54fxxxex24c87b7300f2b9b884bc2"
    # method = "POST"
    # uri = "/api/rest/test/goods/2322333223/infos"
    # params = {}
    # body = ""  # 对于 GET 请求通常没有 body

#     # **JSON 格式的 body**
#     body_dict = {
#     "name": "test",
#     "age": 30,
#     "email": "<EMAIL>"
# }
#     # 将body转换为 JSON 字符串
#     body = json.dumps(body_dict)

    timestamp = str(int(time.time()))

    # 计算签名
    signature = sign(ak, sk, method, uri, params, body, timestamp)

    # 输出结果
    print("===== 签名计算结果 =====")
    print(f"Access Key (ak): {ak}")
    print(f"Secret Key (sk): {sk}")
    print(f"请求方法 (method): {method}")
    print(f"请求 URI (uri): {uri}")
    print(f"请求参数 (params): {params}")
    print(f"请求 Body (body): {body}")
    print(f"时间戳 (timestamp): {timestamp}")
    print(f"计算得到的签名 (sign): {signature}")
